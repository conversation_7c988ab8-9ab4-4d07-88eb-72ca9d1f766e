# permissions for end users to edit servicebindings.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: servicebinding-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - servicebindings
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - servicebindings/status
  verbs:
  - get
