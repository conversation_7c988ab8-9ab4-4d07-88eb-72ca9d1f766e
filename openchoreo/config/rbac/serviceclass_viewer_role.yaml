# permissions for end users to view serviceclasses.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: serviceclass-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - serviceclasses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - serviceclasses/status
  verbs:
  - get
