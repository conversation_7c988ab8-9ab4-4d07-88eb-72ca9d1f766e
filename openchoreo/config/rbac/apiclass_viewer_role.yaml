# permissions for end users to view apiclasses.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: apiclass-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - apiclasses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - apiclasses/status
  verbs:
  - get
