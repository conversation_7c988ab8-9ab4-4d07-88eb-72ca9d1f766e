# permissions for end users to edit apis.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: api-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - apis
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - apis/status
  verbs:
  - get
