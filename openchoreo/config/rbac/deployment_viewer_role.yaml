# permissions for end users to view deployments.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: deployment-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - deployments
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - deployments/status
  verbs:
  - get
