# permissions for end users to edit organizations.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: organization-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - organizations
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - organizations/status
  verbs:
  - get
