# permissions for end users to edit deployableartifacts.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: deployableartifact-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - deployableartifacts
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - deployableartifacts/status
  verbs:
  - get
