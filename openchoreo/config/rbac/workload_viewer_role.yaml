# permissions for end users to view workloads.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: workload-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - workloads
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - workloads/status
  verbs:
  - get
