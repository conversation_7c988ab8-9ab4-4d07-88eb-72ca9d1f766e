# permissions for end users to view releases.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: release-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - releases
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - releases/status
  verbs:
  - get
