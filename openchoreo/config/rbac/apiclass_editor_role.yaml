# permissions for end users to edit apiclasses.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: apiclass-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - apiclasses
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - apiclasses/status
  verbs:
  - get
