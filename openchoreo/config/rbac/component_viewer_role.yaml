# permissions for end users to view components.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: component-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - components
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - components/status
  verbs:
  - get
