# permissions for end users to view gitcommitrequests.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: gitcommitrequest-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - gitcommitrequests
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - gitcommitrequests/status
  verbs:
  - get
