# permissions for end users to view configurationgroups.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: configurationgroup-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - configurationgroups
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - configurationgroups/status
  verbs:
  - get
