# permissions for end users to view projects.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: project-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - projects
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - projects/status
  verbs:
  - get
