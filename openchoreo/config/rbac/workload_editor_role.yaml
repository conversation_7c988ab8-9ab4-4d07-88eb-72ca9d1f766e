# permissions for end users to edit workloads.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: workload-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - workloads
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - workloads/status
  verbs:
  - get
