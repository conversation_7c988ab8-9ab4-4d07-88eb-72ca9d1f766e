# permissions for end users to edit deploymenttracks.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: deploymenttrack-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - deploymenttracks
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - deploymenttracks/status
  verbs:
  - get
