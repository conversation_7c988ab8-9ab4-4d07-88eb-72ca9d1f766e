# permissions for end users to edit environments.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: environment-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - environments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - environments/status
  verbs:
  - get
