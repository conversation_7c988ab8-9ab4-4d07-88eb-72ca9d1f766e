# permissions for end users to view environments.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: environment-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - environments
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - environments/status
  verbs:
  - get
