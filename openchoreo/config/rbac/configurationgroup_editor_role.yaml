# permissions for end users to edit configurationgroups.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: configurationgroup-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - configurationgroups
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - configurationgroups/status
  verbs:
  - get
