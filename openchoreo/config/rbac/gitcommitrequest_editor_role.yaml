# permissions for end users to edit gitcommitrequests.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: gitcommitrequest-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - gitcommitrequests
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - gitcommitrequests/status
  verbs:
  - get
