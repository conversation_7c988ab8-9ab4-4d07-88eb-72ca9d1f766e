# permissions for end users to edit dataplanes.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: dataplane-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - dataplanes
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - dataplanes/status
  verbs:
  - get
