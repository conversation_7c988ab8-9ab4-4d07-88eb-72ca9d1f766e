# permissions for end users to edit webapplications.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: webapplication-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplications
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplications/status
  verbs:
  - get
