# permissions for end users to view organizations.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: organization-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - organizations
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - organizations/status
  verbs:
  - get
