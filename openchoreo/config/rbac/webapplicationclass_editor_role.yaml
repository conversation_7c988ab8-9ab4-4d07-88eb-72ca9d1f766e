# permissions for end users to edit webapplicationclasses.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: webapplicationclass-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplicationclasses
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplicationclasses/status
  verbs:
  - get
