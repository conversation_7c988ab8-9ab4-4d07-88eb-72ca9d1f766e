# permissions for end users to view dataplanes.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: dataplane-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - dataplanes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - dataplanes/status
  verbs:
  - get
