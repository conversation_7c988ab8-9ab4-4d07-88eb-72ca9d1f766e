# This rule is not used by the project choreo itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants read-only access to openchoreo.dev resources.
# This role is intended for users who need visibility into these resources
# without permissions to modify them. It is ideal for monitoring purposes and limited-access viewing.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: buildplane-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - buildplanes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - buildplanes/status
  verbs:
  - get
