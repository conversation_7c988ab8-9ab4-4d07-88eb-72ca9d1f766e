# permissions for end users to edit scheduledtaskclasses.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: scheduledtaskclass-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - scheduledtaskclasses
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - scheduledtaskclasses/status
  verbs:
  - get
