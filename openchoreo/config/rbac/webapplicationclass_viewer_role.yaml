# permissions for end users to view webapplicationclasses.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: webapplicationclass-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplicationclasses
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplicationclasses/status
  verbs:
  - get
