# permissions for end users to view services.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: service-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - services
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - services/status
  verbs:
  - get
