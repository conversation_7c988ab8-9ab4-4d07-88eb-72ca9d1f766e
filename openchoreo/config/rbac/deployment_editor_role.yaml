# permissions for end users to edit deployments.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: deployment-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - deployments/status
  verbs:
  - get
