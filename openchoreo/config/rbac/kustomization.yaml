resources:
  # All RBAC will be applied under this service account in
  # the deployment namespace. You may comment out this resource
  # if your manager will use a service account that exists at
  # runtime. Be sure to update RoleBinding and ClusterRoleBinding
  # subjects if changing service account names.
  - service_account.yaml
  - role.yaml
  - role_binding.yaml
  - leader_election_role.yaml
  - leader_election_role_binding.yaml
  # The following RBAC configurations are used to protect
  # the metrics endpoint with authn/authz. These configurations
  # ensure that only authorized users and service accounts
  # can access the metrics endpoint. Comment the following
  # permissions if you want to disable this protection.
  # More info: https://book.kubebuilder.io/reference/metrics.html
  - metrics_auth_role.yaml
  - metrics_auth_role_binding.yaml
  - metrics_reader_role.yaml
  # For each CRD, "Editor" and "Viewer" roles are scaffolded by
  # default, aiding admins in cluster management. Those roles are
  # not used by the Project itself. You can comment the following lines
  # if you do not want those helpers be installed with your Project.
  - project_editor_role.yaml
  - project_viewer_role.yaml
  - organization_editor_role.yaml
  - organization_viewer_role.yaml
  # For each CRD, "Editor" and "Viewer" roles are scaffolded by
  # default, aiding admins in cluster management. Those roles are
  # not used by the Project itself. You can comment the following lines
  # if you do not want those helpers be installed with your Project.
  - deploymenttrack_editor_role.yaml
  - deploymenttrack_viewer_role.yaml
  - component_editor_role.yaml
  - component_viewer_role.yaml
  - deploymentpipeline_editor_role.yaml
  - deploymentpipeline_viewer_role.yaml
  - dataplane_editor_role.yaml
  - dataplane_viewer_role.yaml
  - environment_editor_role.yaml
  - environment_viewer_role.yaml
  - deployment_editor_role.yaml
  - deployment_viewer_role.yaml
  - deployableartifact_editor_role.yaml
  - deployableartifact_viewer_role.yaml
  # For each CRD, "Admin", "Editor" and "Viewer" roles are scaffolded by
  # default, aiding admins in cluster management. Those roles are
  # not used by the {{ .ProjectName }} itself. You can comment the following lines
  # if you do not want those helpers be installed with your Project.
  - endpoint_admin_role.yaml
  - endpoint_editor_role.yaml
  - endpoint_viewer_role.yaml

  # For each CRD, "Editor" and "Viewer" roles are scaffolded by
  # default, aiding admins in cluster management. Those roles are
  # not used by the Project itself. You can comment the following lines
  # if you do not want those helpers be installed with your Project.
  - workload_editor_role.yaml
  - workload_viewer_role.yaml
  - componentv2_editor_role.yaml
  - componentv2_viewer_role.yaml
  - configurationgroup_editor_role.yaml
  - configurationgroup_viewer_role.yaml
# For each CRD, "Editor" and "Viewer" roles are scaffolded by
# default, aiding admins in cluster management. Those roles are
# not used by the Project itself. You can comment the following lines
# if you do not want those helpers be installed with your Project.
  - release_editor_role.yaml
  - release_viewer_role.yaml
  - scheduledtaskbinding_editor_role.yaml
  - scheduledtaskbinding_viewer_role.yaml
  - scheduledtaskclass_editor_role.yaml
  - scheduledtaskclass_viewer_role.yaml
  - scheduledtask_editor_role.yaml
  - scheduledtask_viewer_role.yaml
  - webapplicationbinding_editor_role.yaml
  - webapplicationbinding_viewer_role.yaml
  - webapplicationclass_editor_role.yaml
  - webapplicationclass_viewer_role.yaml
  - webapplication_editor_role.yaml
  - webapplication_viewer_role.yaml
  - servicebinding_editor_role.yaml
  - servicebinding_viewer_role.yaml
  - serviceclass_editor_role.yaml
  - serviceclass_viewer_role.yaml
  - service_editor_role.yaml
  - service_viewer_role.yaml
  - apibinding_editor_role.yaml
  - apibinding_viewer_role.yaml
  - apiclass_editor_role.yaml
  - apiclass_viewer_role.yaml
  - api_editor_role.yaml
  - api_viewer_role.yaml
  - gitcommitrequest_editor_role.yaml
  - gitcommitrequest_viewer_role.yaml
  - buildplane_admin_role.yaml
  - buildplane_editor_role.yaml
  - buildplane_viewer_role.yaml
  - buildv2_admin_role.yaml
  - buildv2_editor_role.yaml
  - buildv2_viewer_role.yaml
