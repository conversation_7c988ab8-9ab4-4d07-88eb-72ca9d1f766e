# permissions for end users to view apibindings.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: apibinding-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - apibindings
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - apibindings/status
  verbs:
  - get
