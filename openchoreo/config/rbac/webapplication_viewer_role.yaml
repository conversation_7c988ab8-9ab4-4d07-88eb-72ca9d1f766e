# permissions for end users to view webapplications.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: webapplication-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplications
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplications/status
  verbs:
  - get
