# permissions for end users to edit webapplicationbindings.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: webapplicationbinding-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplicationbindings
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplicationbindings/status
  verbs:
  - get
