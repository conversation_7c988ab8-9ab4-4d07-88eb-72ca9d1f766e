# permissions for end users to view webapplicationbindings.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: webapplicationbinding-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplicationbindings
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - webapplicationbindings/status
  verbs:
  - get
