# permissions for end users to edit apibindings.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: apibinding-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - apibindings
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - apibindings/status
  verbs:
  - get
