# permissions for end users to edit releases.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: release-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - releases
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - releases/status
  verbs:
  - get
