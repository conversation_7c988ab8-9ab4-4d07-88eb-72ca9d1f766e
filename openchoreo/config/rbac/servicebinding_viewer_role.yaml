# permissions for end users to view servicebindings.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: servicebinding-viewer-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - servicebindings
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - servicebindings/status
  verbs:
  - get
