# This rule is not used by the project choreo itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants full permissions ('*') over openchoreo.dev.
# This role is intended for users authorized to modify roles and bindings within the cluster,
# enabling them to delegate specific permissions to other users or groups as needed.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: buildv2-admin-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - buildv2s
  verbs:
  - '*'
- apiGroups:
  - openchoreo.dev
  resources:
  - buildv2s/status
  verbs:
  - get
