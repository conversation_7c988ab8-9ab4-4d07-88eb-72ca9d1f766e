# permissions for end users to edit serviceclasses.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: openchoreo
    app.kubernetes.io/managed-by: kustomize
  name: serviceclass-editor-role
rules:
- apiGroups:
  - openchoreo.dev
  resources:
  - serviceclasses
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - openchoreo.dev
  resources:
  - serviceclasses/status
  verbs:
  - get
