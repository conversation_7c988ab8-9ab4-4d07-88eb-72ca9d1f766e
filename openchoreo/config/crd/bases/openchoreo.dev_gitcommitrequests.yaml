---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: gitcommitrequests.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    kind: GitCommitRequest
    listKind: GitCommitRequestList
    plural: gitcommitrequests
    singular: gitcommitrequest
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: GitCommitRequest is the Schema for the gitcommitrequests API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: GitCommitRequestSpec defines the desired state of GitCommitRequest.
            properties:
              authSecretRef:
                description: |-
                  Reference to a Secret that contains write credentials
                  data["username"], data["password"] for HTTPS  **or**
                  data["ssh-privatekey"] for SSH
                type: string
              author:
                description: Author information for the commit
                properties:
                  email:
                    type: string
                  name:
                    type: string
                type: object
              branch:
                description: Branch to commit into
                type: string
              files:
                description: Files to create or patch
                items:
                  properties:
                    content:
                      type: string
                    patch:
                      type: string
                    path:
                      type: string
                  required:
                  - path
                  type: object
                type: array
              message:
                description: The commit message
                type: string
              repoURL:
                description: HTTPS or SSH URL of the repo, e.g. https://github.com/org/repo.git
                type: string
            required:
            - files
            - message
            - repoURL
            type: object
          status:
            description: GitCommitRequestStatus defines the observed state of GitCommitRequest.
            properties:
              message:
                type: string
              observedBranch:
                type: string
              observedSHA:
                type: string
              phase:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
