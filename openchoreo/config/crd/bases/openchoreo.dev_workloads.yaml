---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: workloads.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    kind: Workload
    listKind: WorkloadList
    plural: workloads
    singular: workload
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Workload is the Schema for the workloads API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            properties:
              connections:
                additionalProperties:
                  description: WorkloadConnection represents an internal API connection
                  properties:
                    inject:
                      description: Inject defines how connection details are injected
                        into the workload
                      properties:
                        env:
                          description: Environment variables to inject
                          items:
                            description: WorkloadConnectionEnvVar defines an environment
                              variable injection
                            properties:
                              name:
                                description: Environment variable name
                                type: string
                              value:
                                description: Template value using connection properties
                                  (e.g., "{{ .url }}")
                                type: string
                            required:
                            - name
                            - value
                            type: object
                          type: array
                      required:
                      - env
                      type: object
                    params:
                      additionalProperties:
                        type: string
                      description: Parameters for connection configuration (dynamic
                        key-value pairs)
                      type: object
                    type:
                      description: Type of connection - only "api" for now
                      enum:
                      - api
                      type: string
                  required:
                  - inject
                  - type
                  type: object
                description: |-
                  Connections define how this workload consumes internal and external resources.
                  The key is the connection name, and the value is the connection specification.
                type: object
              containers:
                additionalProperties:
                  description: Container represents a single container in the workload.
                  properties:
                    args:
                      items:
                        type: string
                      type: array
                    command:
                      description: Container entrypoint & args.
                      items:
                        type: string
                      type: array
                    env:
                      description: Explicit environment variables.
                      items:
                        description: EnvVar represents an environment variable present
                          in the container.
                        properties:
                          key:
                            description: The environment variable key.
                            type: string
                          value:
                            description: |-
                              The literal value of the environment variable.
                              Mutually exclusive with valueFrom.
                            type: string
                          valueFrom:
                            description: |-
                              Extract the environment variable value from another resource.
                              Mutually exclusive with value.
                            properties:
                              configurationGroupRef:
                                description: Reference to a configuration group.
                                properties:
                                  key:
                                    type: string
                                  name:
                                    type: string
                                required:
                                - key
                                - name
                                type: object
                              secretRef:
                                description: Reference to a secret resource.
                                properties:
                                  key:
                                    type: string
                                  name:
                                    type: string
                                required:
                                - key
                                - name
                                type: object
                            type: object
                        required:
                        - key
                        type: object
                      type: array
                    image:
                      description: OCI image to run (digest or tag).
                      minLength: 1
                      type: string
                  required:
                  - image
                  type: object
                description: |-
                  Containers define the container specifications for this workload.
                  The key is the container name, and the value is the container specification.
                type: object
              endpoints:
                additionalProperties:
                  description: WorkloadEndpoint represents a simple network endpoint
                    for basic exposure.
                  properties:
                    port:
                      description: Port number for the endpoint.
                      format: int32
                      maximum: 65535
                      minimum: 1
                      type: integer
                    schema:
                      description: |-
                        Optional schema for the endpoint.
                        This can be used to define the actual API definition of the endpoint that is exposed by the workload.
                      properties:
                        content:
                          type: string
                        type:
                          type: string
                      type: object
                    type:
                      description: Type indicates the protocol/technology of the endpoint
                        (HTTP, REST, gRPC, GraphQL, Websocket, TCP, UDP).
                      enum:
                      - HTTP
                      - REST
                      - gRPC
                      - GraphQL
                      - Websocket
                      - TCP
                      - UDP
                      type: string
                  required:
                  - port
                  - type
                  type: object
                description: |-
                  Endpoints define simple network endpoints for basic port exposure.
                  The key is the endpoint name, and the value is the endpoint specification.
                type: object
              owner:
                properties:
                  componentName:
                    minLength: 1
                    type: string
                  projectName:
                    minLength: 1
                    type: string
                required:
                - componentName
                - projectName
                type: object
            required:
            - owner
            type: object
          status:
            description: WorkloadStatus defines the observed state of Workload.
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
