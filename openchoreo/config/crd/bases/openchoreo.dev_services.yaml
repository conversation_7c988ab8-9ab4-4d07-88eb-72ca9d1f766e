---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: services.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    kind: Service
    listKind: ServiceList
    plural: services
    singular: service
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Service is the Schema for the services API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ServiceSpec defines the desired state of Service.
            properties:
              apis:
                additionalProperties:
                  properties:
                    className:
                      default: default
                      type: string
                    rest:
                      properties:
                        backend:
                          properties:
                            basePath:
                              type: string
                            port:
                              format: int32
                              type: integer
                          required:
                          - port
                          type: object
                        exposeLevels:
                          items:
                            type: string
                          type: array
                      type: object
                    type:
                      description: EndpointType defines the different API technologies
                        supported by the endpoint
                      type: string
                  required:
                  - className
                  - type
                  type: object
                type: object
              className:
                default: default
                description: ClassName is the name of the service class that provides
                  the service-specific deployment configuration.
                type: string
              overrides:
                additionalProperties:
                  type: boolean
                type: object
              owner:
                properties:
                  componentName:
                    minLength: 1
                    type: string
                  projectName:
                    minLength: 1
                    type: string
                required:
                - componentName
                - projectName
                type: object
              workloadName:
                description: WorkloadName is the name of the workload that this service
                  is referencing.
                type: string
            required:
            - className
            - owner
            - workloadName
            type: object
          status:
            description: ServiceStatus defines the observed state of Service.
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
