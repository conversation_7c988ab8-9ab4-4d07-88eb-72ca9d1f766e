---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: apibindings.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    kind: APIBinding
    listKind: APIBindingList
    plural: apibindings
    singular: apibinding
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: APIBinding is the Schema for the apibindings API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: APIBindingSpec defines the desired state of APIBinding.
            properties:
              apiClassName:
                description: APIClassName specifies the APIClass to use for this binding
                minLength: 1
                type: string
              apiName:
                description: APIName specifies the API resource to bind
                minLength: 1
                type: string
              environmentName:
                description: Environment specifies the target environment for this
                  binding
                minLength: 1
                type: string
            required:
            - apiClassName
            - apiName
            - environmentName
            type: object
          status:
            description: APIBindingStatus defines the observed state of APIBinding.
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
