# This kustomization.yaml is not intended to be run by itself,
# since it depends on service name and namespace that are out of this kustomize package.
# It should be run by config/default
resources:
  - bases/openchoreo.dev_organizations.yaml
  - bases/openchoreo.dev_projects.yaml
  - bases/openchoreo.dev_environments.yaml
  - bases/openchoreo.dev_dataplanes.yaml
  - bases/openchoreo.dev_deploymentpipelines.yaml
  - bases/openchoreo.dev_components.yaml
  - bases/openchoreo.dev_deploymenttracks.yaml
  - bases/openchoreo.dev_deployableartifacts.yaml
  - bases/openchoreo.dev_deployments.yaml
  - bases/openchoreo.dev_endpoints.yaml
  - bases/openchoreo.dev_configurationgroups.yaml
  - bases/openchoreo.dev_componentv2s.yaml
  - bases/openchoreo.dev_workloads.yaml
  - bases/openchoreo.dev_gitcommitrequests.yaml
  - bases/openchoreo.dev_apis.yaml
  - bases/openchoreo.dev_apiclasses.yaml
  - bases/openchoreo.dev_apibindings.yaml
  - bases/openchoreo.dev_services.yaml
  - bases/openchoreo.dev_serviceclasses.yaml
  - bases/openchoreo.dev_servicebindings.yaml
  - bases/openchoreo.dev_webapplications.yaml
  - bases/openchoreo.dev_webapplicationclasses.yaml
  - bases/openchoreo.dev_webapplicationbindings.yaml
  - bases/openchoreo.dev_scheduledtasks.yaml
  - bases/openchoreo.dev_scheduledtaskclasses.yaml
  - bases/openchoreo.dev_scheduledtaskbindings.yaml
  - bases/openchoreo.dev_releases.yaml
  - bases/openchoreo.dev_buildv2s.yaml
  - bases/openchoreo.dev_buildplanes.yaml
# +kubebuilder:scaffold:crdkustomizeresource

# patches:
# [WEBHOOK] To enable webhook, uncomment all the sections with [WEBHOOK] prefix.
# patches here are for enabling the conversion webhook for each CRD
# +kubebuilder:scaffold:crdkustomizewebhookpatch

# [CERTMANAGER] To enable cert-manager, uncomment all the sections with [CERTMANAGER] prefix.
# patches here are for enabling the CA injection for each CRD
# +kubebuilder:scaffold:crdkustomizecainjectionpatch

# [WEBHOOK] To enable webhook, uncomment the following section
# the following config is for teaching kustomize how to do kustomization for CRDs.
#configurations:
#- kustomizeconfig.yaml
