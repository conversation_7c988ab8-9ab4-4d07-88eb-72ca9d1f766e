## Append samples of your project ##
resources:
  - openchoreo_v1alpha1_api.yaml
  - openchoreo_v1alpha1_apibinding.yaml
  - openchoreo_v1alpha1_apiclass.yaml
  - openchoreo_v1alpha1_component.yaml
  - openchoreo_v1alpha1_componentv2.yaml
  - openchoreo_v1alpha1_configurationgroup.yaml
  - openchoreo_v1alpha1_dataplane.yaml
  - openchoreo_v1alpha1_deployableartifact.yaml
  - openchoreo_v1alpha1_deployment.yaml
  - openchoreo_v1alpha1_deploymentpipeline.yaml
  - openchoreo_v1alpha1_deploymenttrack.yaml
  - openchoreo_v1alpha1_endpoint.yaml
  - openchoreo_v1alpha1_environment.yaml
  - openchoreo_v1alpha1_gitcommitrequest.yaml
  - openchoreo_v1alpha1_organization.yaml
  - openchoreo_v1alpha1_project.yaml
  - openchoreo_v1alpha1_release.yaml
  - openchoreo_v1alpha1_scheduledtask.yaml
  - openchoreo_v1alpha1_scheduledtaskbinding.yaml
  - openchoreo_v1alpha1_scheduledtaskclass.yaml
  - openchoreo_v1alpha1_service.yaml
  - openchoreo_v1alpha1_servicebinding.yaml
  - openchoreo_v1alpha1_serviceclass.yaml
  - openchoreo_v1alpha1_webapplication.yaml
  - openchoreo_v1alpha1_webapplicationbinding.yaml
  - openchoreo_v1alpha1_webapplicationclass.yaml
  - openchoreo_v1alpha1_workload.yaml
  - openchoreo_v1alpha1_buildv2.yaml
  - openchoreo_v1alpha1_buildplane.yaml
# +kubebuilder:scaffold:manifestskustomizesamples
