---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: mutating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /mutate-openchoreo-dev-v1alpha1-project
  failurePolicy: Ignore # TODO: Change to Fail after implementing the webhook logic
  name: mproject-v1alpha1.kb.io
  rules:
  - apiGroups:
    - openchoreo.dev
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - projects
  sideEffects: None
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: validating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-openchoreo-dev-v1alpha1-project
  failurePolicy: Ignore # TODO: Change to Fail after implementing the webhook logic
  name: vproject-v1alpha1.kb.io
  rules:
  - apiGroups:
    - openchoreo.dev
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - projects
  sideEffects: None
