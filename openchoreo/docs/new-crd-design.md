# New CRD Design - Project Model & Claim/Class Pattern

This document explains the new CRD design for OpenChoreo that follows the Developer abstractions with the Claim/Class pattern.
This design aims to provide better abstractions for developers while allowing platform engineers to maintain control over configuration and resource provisioning.

## Design Principles

### Claim/Class Pattern with Environment Bindings

* **Classes**: Platform Engineers (PEs) define templates and governance rules via Classes
* **Claims**: Developers create environment-independent intent resources
* **Bindings**: Environment-bound copies of Claims generated by controllers
* **Releases**: Internal resources with final rendered Kubernetes resources

### Developer vs. Platform Engineer Separation

* **Developers** think in terms of: Project, Component, Build, Workload, Connections, APIs
* **Platform Engineers** define: Classes, policies, resource templates, governance rules
* **Controllers** handle: Resource generation, validation, orchestration, environment binding

## Developer-Facing Abstractions

### Project

**Purpose**: Logical grouping of related components that collectively define an application workloads or bounded context

**Key Features**:

* Serves as the main organizational boundary for source code, deployments, and network policies
* Defines team or application-level boundaries
* Governs internal access across components
* Provides deployment isolation and visibility scope
* Controls component communication internally and with external systems

**Current Implementation**: ProjectV2 (`api/v1/project_types.go`)

### Component

**Purpose**: Deployable piece of software (service, webapp, task, API proxy)

**Key Features**:

* Belongs to exactly one Project
* Declares its type (service, webapp, task, api)
* References source code location (Git repo, image registry)
* Environment-agnostic (not bound to specific environments)
* Orchestrates creation of environment-independent Workload, API, and Connection resources

**Current Implementation**: ComponentV2 (`api/v1/componentv2_types.go`)

### DeploymentTrack

**Purpose**: Represents a line of development or delivery stream for a component

**Key Features**:

* Aligned with Git branches (main, release-1.0, feature-xyz)
* Can represent API versions (v1, v2) or meaningful evolution labels
* Optional for simple use cases (defaults to "default" track)
* Enables multiple deployment streams from same codebase

**NOTE**: This is an optional abstraction, not required for all components. The initial implementation does not include the DeploymentTrack support, but it can be added later if needed.

### API

**Purpose**: Managed API endpoints with full API management capabilities

**Key Features**:

* For managed API types only (REST, GraphQL) that support API management
* Simple network endpoints (TCP, UDP) are defined in Workload spec instead
* Includes HTTP routing, versioning, authentication, rate limits, scopes
* Independently versioned from component
* Enables controlled exposure (organization-wide or public)
* Integrates with API gateways and developer portals

**Current Implementation**: API with API Management features (`api/v1/endpointv2_types.go`)
**TODO**: Rename from EndpointV2 to API to reflect its purpose

## Core CRD Architecture

### Component-Type-Specific Pattern

The architecture uses component-type-specific resources (Service, ScheduledTask, WebApplication, ProxyAPI) that reference Workload as a pure data structure. Each component type has its own controller and class for platform governance.

#### Workload (Pure Data Structure)

**Purpose**: Environment-independent runtime contract that captures everything needed to run a component

**Key Features**:

* **Runtime Contract**: Defines container image, ports, environment variables, and runtime dependencies
* **Pure Data**: No controller - used as data by component-type-specific controllers
* **Environment-independent**: Not bound to specific environments
* **Simple Endpoints**: Includes basic network endpoints (TCP, UDP) in spec
* **Manual Creation**: Created manually by developers or generated by CLI tools/pipelines
* **Code Reuse**: Can be referenced by multiple component types (Service, ScheduledTask, etc.)

**File**: `api/v1/workload_types.go`

### Component Type Resources

#### Service

**Purpose**: Microservice component with optional API exposure capabilities

**Key Features**:

* **References Workload**: Points to Workload for runtime contract
* **Service-Specific Config**: Includes replicas and other service-specific settings
* **API Integration**: Can define managed APIs inline (REST, GraphQL, etc.)
* **Class Reference**: References ServiceClass for platform governance
* **Environment Bindings**: Creates ServiceBinding for environment deployment

**File**: `api/v1/service_types.go` (new)

#### ServiceClass

**Purpose**: Platform Engineer-defined templates for service configurations

**Key Features**:

* **Deployment Template**: Defines resource limits, deployment strategies, sidecars
* **Service Template**: Kubernetes Service configuration
* **Platform Governance**: Enforces security policies, monitoring, compliance
* **Sidecar Injection**: Can inject monitoring, logging, security sidecars

**File**: `api/v1/serviceclass_types.go` (new)

#### ServiceBinding

**Purpose**: Environment-bound deployment of a Service

**Key Features**:

* **Environment-Specific**: Bound to specific environment (dev, staging, prod)
* **Workload Snapshot**: Contains full snapshot of referenced Workload for isolation
* **API Definitions**: Includes API specifications for unified promotion
* **Self-Sufficient**: Contains everything needed for environment deployment
* **Class Reference**: Still references ServiceClass for ongoing governance

**File**: `api/v1/servicebinding_types.go` (new)

#### ServiceRelease

**Purpose**: Internal resource with final rendered Kubernetes resources for Service

**Key Features**:

* **Generated by ServiceBinding**: Created by ServiceBinding controller
* **Not Committed**: Not stored in GitOps repository
* **Applied Directly**: Applied to data plane clusters
* **Contains K8s Resources**: Deployments, Services, NetworkPolicies, etc.

**File**: `api/v1/servicerelease_types.go` (new)

#### ScheduledTask

**Purpose**: Scheduled task/job component

**Key Features**:

* **References Workload**: Points to Workload for runtime contract
* **Task-Specific Config**: Includes cronSchedule and job-specific settings
* **Class Reference**: References ScheduledTaskClass for platform governance
* **Environment Bindings**: Creates ScheduledTaskBinding for environment deployment

**File**: `api/v1/scheduledtask_types.go` (new)

#### ScheduledTaskClass

**Purpose**: Platform Engineer-defined templates for scheduled task configurations

**Key Features**:

* **Job Template**: Defines resource limits, job policies, retry strategies
* **Platform Governance**: Enforces scheduling policies, resource constraints
* **Monitoring Integration**: Can include job monitoring and alerting configurations

**File**: `api/v1/scheduledtaskclass_types.go` (new)

#### ScheduledTaskBinding & ScheduledTaskRelease

Similar patterns as Service but for scheduled tasks, creating CronJobs instead of Deployments.

### API Management

#### APIClass

**Purpose**: Platform Engineer-defined templates for managed API configurations

**Key Features**:

* Defines base configurations for managed API types (REST, GraphQL)
* Configures exposure levels: Project, Organization, Public
* Sets API Management policies (rate limiting, authentication)
* Some API types may not support all exposure levels
* Referenced by APIs embedded in component types (Service, ProxyAPI, etc.)

**File**: `api/v1/apiclass_types.go` (renamed from endpointclass\_types.go)

#### API Integration Patterns

**Embedded APIs**: APIs can be defined inline within component-type resources:

* **Service APIs**: Defined in `Service.spec.apis` for APIs exposed by microservices
* **ProxyAPI APIs**: For API proxying to external services (Salesforce, Stripe, etc.)
* **Unified Promotion**: APIs move with their parent component as single unit

**Standalone API Resources**: For shared or independent API management scenarios:

* **Separate Lifecycle**: Independent versioning and management
* **Cross-Component Sharing**: APIs that serve multiple components
* **External Integration**: APIs that proxy to external systems

#### API Features (Both Embedded and Standalone)

**Key Capabilities**:

* **Managed API Types**: REST, GraphQL, gRPC with full API management
* **Simple Endpoints**: TCP, UDP endpoints remain in Workload specifications
* **Multi-Level Exposure**:

  * **Project**: Accessible only within the project
  * **Organization**: Accessible across projects within organization
  * **Public**: Exposed to the internet
* **Per-Operation Control**: Different exposure levels and scopes per API operation
* **Security Integration**: Scope-based authorization, JWT validation
* **Backend Targets**: Can target Service workloads or external systems

## Runtime & Deployment Abstractions

### Environment

**Purpose**: Target runtime space (dev, staging, prod) with infrastructure-specific configuration

**Key Features**:

* Encapsulates cluster bindings, gateway setups, secrets, policies
* Where actual deployments occur
* May have constraints/validations (e.g., only approved workloads in prod)
* Infrastructure-specific configuration separate from application logic

### Binding Resources

**Purpose**: Environment-bound copies of component-type-specific resources

**Key Features**:

* **ServiceBinding**: Environment-bound copy of Service (includes workload snapshot and APIs)
* **ScheduledTaskBinding**: Environment-bound copy of ScheduledTask
* **WebApplicationBinding**: Environment-bound copy of WebApplication
* **ProxyAPIBinding**: Environment-bound copy of ProxyAPI
* **ConnectionBinding**: Environment-bound copy of Connection (future)
* **Promotion Vehicle**: Enables progressive delivery (dev → staging → prod)
* **Snapshot-Based**: Contains full snapshots for environment isolation
* **GitOps Committed**: Stored in GitOps repository for audit trail
* **Self-Sufficient**: Contains everything needed for deployment in target environment

**Binding Generation**:

* Developers author: Component (optionally creates Service/ScheduledTask) + Workload
* Controllers generate: Component-type-specific bindings for each environment
* Promotion = copying component-type bindings to different environments in GitOps

## Controller Flow & GitOps Integration

### Direct Kubernetes Mode

1. Developer applies ComponentV2 CRD and Workload resource
2. Component controller creates component-type-specific resources (Service, ScheduledTask, etc.) if defined inline
3. Component-type controllers (Service, ScheduledTask, etc.) create their respective Binding resources for development environment
4. For promotion: manually copy/create additional Bindings for staging/prod environments
5. Binding controllers (ServiceBinding, ScheduledTaskBinding, etc.) create Release resources
6. Release controllers apply final rendered resources to data plane clusters

### GitOps Mode (with Flux)

1. Developer applies ComponentV2 CRD and Workload resource (synced by Flux)
2. Component controller generates component-type resource YAMLs (Service, ScheduledTask, etc.) and commits to GitHub
3. Flux syncs the generated component-type resources to cluster
4. Component-type controllers generate Binding YAMLs for development environment and commit to GitHub
5. For promotion: copy Binding resources to staging/prod environment directories in GitOps repo
6. Flux syncs the Binding resources to cluster
7. Binding controllers create Release resources directly in cluster (not committed to GitOps)
8. Release controllers apply final rendered resources to data plane clusters

### GitCommitRequest

**Purpose**: Optional controller for GitOps workflow automation

**Key Features**:

* Only used when GitOps feature is enabled
* Used by Component and component-type controllers (Service, ScheduledTask, etc.) to commit resources to GitOps repository
* Commits ComponentV2, Workload, component-type resources (Service, ScheduledTask, etc.), and their Binding copies to GitOps repository
* Supports both mono-repo and multi-repo setups
* **Note**: Currently under development with security improvements needed

**File**: `api/v1/gitcommitrequest_types.go`

### Benefits of New Design

* **Component-Type Specialization**: Service, ScheduledTask, WebApplication, ProxyAPI each have dedicated controllers matching operational reality
* **Unified Promotion**: Component and its APIs move together as single binding unit per environment
* **Runtime Config Placement**: Service-specific configs (replicas), task-specific configs (cronSchedule) have natural homes
* **Workload Reusability**: Same workload contract can be used by multiple component types
* **Platform Governance**: Type-specific classes (ServiceClass, ScheduledTaskClass) provide appropriate governance
* **Environment Isolation**: Snapshot-based bindings prevent configuration drift
* **Clean Separation**: GitOps-committed resources vs runtime-only resources
* **Audit Trail**: All user-facing intent preserved in Git while keeping cluster operations clean

## References

Sample YAMLs and examples for the new CRD design can be found in the `new-design-sample` directory.
